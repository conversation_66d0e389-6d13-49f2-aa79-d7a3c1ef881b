default: &default
  base_url: "https://loanpro.simnang.com/api/public/api/1/"
  calculator_base_url: "https://lms.loanpro.io/api/v2/calculator"
  api_token: <%= ENV.fetch('LOANPRO_TOKEN', nil) %>
  instance_id: <%= ENV.fetch('LOANPRO_INSTANCE_ID', nil) %>
  # This is a replacement for Identity Service LOAN_PRO_BEARER_TOKEN env variable
  webhook_token: <%= ENV.fetch('LOANPRO_WEBHOOK_TOKEN', nil) %>
  ba_processor: '4'
  portfolios:
    autopay_portfolio_id: 49
    ownership_portfolio_id: 103
    state_portfolio_id: 105
  subportfolios:
    autopay_enabled_subportfolio_id: 60
    autopay_disabled_subportfolio_id: 61
    crb_originated_subportfolio_id: 3
    upl_subportfolio_id: 90
    states:
      AL: 10
      AK: 11
      AZ: 12
      AR: 13
      CA: 14
      CO: 15
      CT: 16
      DE: 17
      FL: 18
      GA: 19
      HI: 20
      ID: 21
      IL: 22
      IN: 23
      IA: 24
      KS: 25
      KY: 26
      LA: 27
      ME: 28
      MD: 29
      MA: 30
      MI: 31
      MN: 32
      MS: 33
      MO: 34
      MT: 35
      NE: 36
      NV: 37
      NH: 38
      NJ: 39
      NM: 40
      NY: 41
      NC: 42
      ND: 43
      OH: 44
      OK: 45
      OR: 46
      PA: 47
      RI: 48
      SC: 49
      SD: 50
      TN: 51
      TX: 52
      UT: 53
      VT: 54
      VA: 55
      WA: 56
      WV: 57
      WI: 58
      WY: 59
      DC: 89
  custom_fields:
    checking_account_entity_ids: 125
    plaid_tokens: 126
    plaid_item_ids: 127
    plaid_account_ids: 128
    plaid_account_last_fours: 131
    plaid_real_account_last_fours: 130
  checklist_items:
    socure_monitoring_is_active: 81
    stamped_contract_letter_sent: 82
  dynamic_templates:
    amortization: 28

development:
  <<: *default

test:
  <<: *default

sandbox:
  <<: *default

staging:
  <<: *default

production:
  <<: *default
  ba_processor: '87'
  custom_fields:
    checking_account_entity_ids: 117
    plaid_tokens: 116
    plaid_item_ids: 118
    plaid_account_ids: 119
    plaid_account_last_fours: 120
    plaid_real_account_last_fours: 121
  checklist_items:
    socure_monitoring_is_active: 68
    stamped_contract_letter_sent: 69
  dynamic_templates:
    amortization: 44
