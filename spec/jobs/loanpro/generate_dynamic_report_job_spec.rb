# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loanpro::GenerateDynamicReportJob do
  let!(:loanpro_loan) { create(:loanpro_loan, til_sign_date: Date.today) }
  let(:channel_key) { "loanpro_report:#{loanpro_loan.loan.borrower_id}:#{uuid}" }
  let(:reschedule_params) { [uuid, loanpro_loan.loanpro_loan_id, 'amortization', 1] }
  let(:uuid) { SecureRandom.uuid }
  let(:file) do
    {
      'created' => "/Date(#{Time.current.to_i})/",
      'status' => 'dataDumpProcess.status.complete',
      'url' => 'http://127.0.0.1/sample.pdf'
    }
  end

  subject(:job) { described_class.new }

  describe '#perform' do
    it 'provides an existing URL' do
      allow(Clients::LoanproApi).to receive(:download_dynamic_template).and_return('results' => [file])

      expect do
        job.perform(uuid, loanpro_loan.loanpro_loan_id, 'amortization')
      end.to have_broadcasted_to(channel_key).with(hash_including(status: 'complete', url: file['url']))
    end

    it 'does not trigger a new report when there is one in progress' do
      file.merge!('status' => 'dataDumpProcess.status.inProgress')
      allow(Clients::LoanproApi).to receive(:download_dynamic_template).and_return('results' => [file])
      expect(Clients::LoanproApi).not_to receive(:generate_dynamic_template)

      expect(ActionCable.server).not_to receive(:broadcast)
      expect(described_class).to receive(:perform_async).with(*reschedule_params, wait: 5.seconds)

      job.perform(uuid, loanpro_loan.loanpro_loan_id, 'amortization')
    end

    it 'does trigger a new report when there is no existing one' do
      allow(Clients::LoanproApi).to receive(:download_dynamic_template).and_return('results' => [])
      expect(Clients::LoanproApi).to receive(:generate_dynamic_template)

      expect(ActionCable.server).not_to receive(:broadcast)
      expect(described_class).to receive(:perform_async).with(*reschedule_params, wait: 5.seconds)

      job.perform(uuid, loanpro_loan.loanpro_loan_id, 'amortization')
    end

    it 'requests a new report when the existing one is outdated' do
      file.merge!('created' => "/Date(#{2.days.ago.to_i})/")
      allow(Clients::LoanproApi).to receive(:download_dynamic_template).and_return('results' => [file])
      expect(Clients::LoanproApi).to receive(:generate_dynamic_template)

      expect(ActionCable.server).not_to receive(:broadcast)
      expect(described_class).to receive(:perform_async).with(*reschedule_params, wait: 5.seconds)

      job.perform(uuid, loanpro_loan.loanpro_loan_id, 'amortization')
    end

    it 'reports a failure after maximum attempts' do
      expect do
        job.perform(uuid, loanpro_loan.loanpro_loan_id, 'amortization', described_class::MAX_ATTEMPTS)
      end.to have_broadcasted_to(channel_key).with(hash_including(status: 'failed'))
    end

    it 'reports a failure when the status is invalid' do
      file.merge!('status' => 'dataDumpProcess.status.failed')
      allow(Clients::LoanproApi).to receive(:download_dynamic_template).and_return('results' => [file])
      expect(Clients::LoanproApi).not_to receive(:generate_dynamic_template)

      expect do
        job.perform(uuid, loanpro_loan.loanpro_loan_id, 'amortization')
      end.to have_broadcasted_to(channel_key).with(hash_including(status: 'failed'))
    end
  end
end
