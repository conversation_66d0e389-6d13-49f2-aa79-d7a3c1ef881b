# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Talkdesk::InCooldown, type: :service do
  include ActiveSupport::Testing::TimeHelpers

  let(:loan) { create(:ipl_loan) }
  let(:loan_id) { loan.id }
  let(:other_loan) { create(:ipl_loan) }
  let(:other_loan_id) { other_loan.id }
  let(:phone_number) { '5556667788' }
  let(:other_number) { '1112223344' }
  let(:loan_app_status) { 'PENDING' }

  before do
    travel_to(Time.zone.now)
  end

  describe 'validations' do
    it { should validate_presence_of(:loan_id) }
    it { should validate_presence_of(:phone_number) }
  end

  describe '.call' do
    it 'indicates a borrower is not in cooldown when they have no talkdesk events' do
      create(:talkdesk_event,
             loan_id: create(:ipl_loan).id,
             phone_number: '0000000000',
             disposition: TalkdeskEvent::TERMINAL_DISPOSITION)
      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(false)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(false)
    end

    it 'indicates a borrower is not in cooldown when they only have uncalled events' do
      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::QUEUED_DISPOSITION)
      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::QUEUE_REMOVED_DISPOSITION)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(false)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(false)
    end

    it 'indicates a borrower is not in cooldown when an applicable event has no called_at' do
      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::AWAITING_ACTION_DISPOSITION,
             called_at: nil)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(false)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(false)
    end

    context 'terminal event' do
      it 'indicates a borrower is in cooldown when they have a terminal event at any point' do
        create(:talkdesk_event,
               loan_id:,
               phone_number:,
               disposition: TalkdeskEvent::TERMINAL_DISPOSITION,
               called_at: 100.years.ago)

        expect(described_class.call(loan_id:, phone_number: other_number)).to eq(true)
        expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(true)
      end

      it 'indicates a borrower is in cooldown when they have a terminal event even if we do not know when' do
        create(:talkdesk_event,
               loan_id:,
               phone_number:,
               disposition: TalkdeskEvent::TERMINAL_DISPOSITION,
               called_at: nil)

        expect(described_class.call(loan_id:, phone_number: other_number)).to eq(true)
        expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(true)
      end

      it 'indicates a borrower is not in cooldown when they accepted TCPA after the terminal event' do
        create(:talkdesk_event,
               loan_id:,
               phone_number:,
               disposition: TalkdeskEvent::TERMINAL_DISPOSITION,
               called_at: 30.seconds.ago)

        loan.borrower.update(tcpa_accepted_at: 25.seconds.ago)
        other_loan.borrower.update(tcpa_accepted_at: 25.seconds.ago)

        expect(described_class.call(loan_id:, phone_number: other_number)).to eq(false)
        expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(false)
      end

      it 'indicates a borrower is in cooldown when they accepted TCPA before the terminal event' do
        create(:talkdesk_event,
               loan_id:,
               phone_number:,
               disposition: TalkdeskEvent::TERMINAL_DISPOSITION,
               called_at: 30.seconds.ago)

        loan.borrower.update(tcpa_accepted_at: 35.seconds.ago)
        other_loan.borrower.update(tcpa_accepted_at: 35.seconds.ago)

        expect(described_class.call(loan_id:, phone_number: other_number)).to eq(true)
        expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(true)
      end
    end

    it 'indicates a borrower is in cooldown when they have recently indicated they are taking action' do
      # An event that's just a little too long ago
      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::AWAITING_ACTION_DISPOSITION,
             called_at: (36.hours + 1.minute).ago)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(false)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(false)

      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::AWAITING_ACTION_DISPOSITION,
             called_at: (36.hours - 1.minute).ago)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(true)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(true)
    end

    it 'indicates a borrower is in cooldown when they have recently been spoken with' do
      # An event that's just a little too long ago
      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::BASIC_CONTACT_DISPOSITION,
             called_at: (24.hours + 1.minute).ago)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(false)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(false)

      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::BASIC_CONTACT_DISPOSITION,
             called_at: (24.hours - 1.minute).ago)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(true)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(true)
    end

    it 'indicates a borrower is in cooldown when we have very recently attempted contact' do
      # An event that's just a little too long ago
      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::NO_CONTACT_DISPOSITION,
             called_at: (6.hours + 1.minute).ago)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(false)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(false)

      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::NO_CONTACT_DISPOSITION,
             called_at: (6.hours - 1.minute).ago)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(true)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(true)
    end

    it 'indicates a borrower is in cooldown when we are unable to determine a very recent disposition' do
      # An event that's just a little too long ago
      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::UNKNOWN_DISPOSITION,
             called_at: (6.hours + 1.minute).ago)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(false)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(false)

      create(:talkdesk_event,
             loan_id:,
             phone_number:,
             disposition: TalkdeskEvent::UNKNOWN_DISPOSITION,
             called_at: (6.hours - 1.minute).ago)

      expect(described_class.call(loan_id:, phone_number: other_number)).to eq(true)
      expect(described_class.call(loan_id: other_loan_id, phone_number:)).to eq(true)
    end

    context 'app status dial cap' do
      it 'indicates a borrower is in cooldown when max dials for loan app status is reached' do
        3.times do
          create(:talkdesk_event,
                 loan_id:,
                 phone_number:,
                 disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
                 direction: TalkdeskEvent::OUTBOUND_DIRECTION,
                 loan_app_status:)
        end

        expect(described_class.call(loan_id:, phone_number:, loan_app_status:)).to eq(true)
      end

      it 'does not indicate a borrower is in cooldown when max dials have not been reached' do
        2.times do
          create(:talkdesk_event,
                 loan_id:,
                 phone_number:,
                 disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
                 direction: TalkdeskEvent::OUTBOUND_DIRECTION,
                 loan_app_status:)
        end

        expect(described_class.call(loan_id:, phone_number:, loan_app_status:)).to eq(false)
      end

      it 'does not count inbound calls toward the dial cap' do
        2.times do
          create(:talkdesk_event,
                 loan_id:,
                 phone_number:,
                 disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
                 direction: TalkdeskEvent::OUTBOUND_DIRECTION,
                 loan_app_status:)
        end
        create(:talkdesk_event,
               loan_id:,
               phone_number:,
               disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
               direction: TalkdeskEvent::INBOUND_DIRECTION,
               loan_app_status:)

        expect(described_class.call(loan_id:, phone_number:, loan_app_status:)).to eq(false)
      end

      it 'does not count calls made at a different app status toward the cap' do
        2.times do
          create(:talkdesk_event,
                 loan_id:,
                 phone_number:,
                 disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
                 direction: TalkdeskEvent::OUTBOUND_DIRECTION,
                 loan_app_status: 'PENDING')
        end
        create(:talkdesk_event,
               loan_id:,
               phone_number:,
               disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
               direction: TalkdeskEvent::OUTBOUND_DIRECTION,
               loan_app_status: 'OFFERED')

        expect(described_class.call(loan_id:, phone_number:, loan_app_status: 'PENDING')).to eq(false)
      end

      it 'does not count calls associated with a different loan app toward the cap' do
        2.times do
          create(:talkdesk_event,
                 loan_id:,
                 phone_number:,
                 disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
                 direction: TalkdeskEvent::OUTBOUND_DIRECTION,
                 loan_app_status: 'PENDING')
        end
        create(:talkdesk_event,
               loan_id: other_loan_id, # different loan id, shouldn't count toward cap
               phone_number:,
               disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
               direction: TalkdeskEvent::OUTBOUND_DIRECTION,
               loan_app_status: 'PENDING')

        expect(described_class.call(loan_id:, phone_number:, loan_app_status: 'PENDING')).to eq(false)
      end

      it 'only counts dispositions indicating a dial was made toward the dial cap' do
        non_dialed_dispositions = TalkdeskEvent::DISPOSITIONS - Talkdesk::InCooldown::DIALED_DISPOSITIONS
        non_dialed_dispositions.delete(TalkdeskEvent::TERMINAL_DISPOSITION) # because a terminal disposition call always causes cooldown to be true

        2.times do
          create(:talkdesk_event,
                 loan_id:,
                 phone_number:,
                 disposition: Talkdesk::InCooldown::DIALED_DISPOSITIONS.sample,
                 direction: TalkdeskEvent::OUTBOUND_DIRECTION,
                 loan_app_status:)
        end
        create(:talkdesk_event,
               loan_id:,
               phone_number:,
               disposition: non_dialed_dispositions.sample,
               direction: TalkdeskEvent::OUTBOUND_DIRECTION,
               loan_app_status:)

        expect(described_class.call(loan_id:, phone_number:, loan_app_status:)).to eq(false)
      end
    end
  end
end
