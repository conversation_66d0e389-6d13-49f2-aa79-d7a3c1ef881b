# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::CreditDisclosures::Show, type: :service do
  let!(:loan) { create(:loan) }
  let!(:loan_credit_data) { create(:loan_credit_data, loan:) }
  let(:q) { loan_credit_data.credit_report_hash }
  let(:params) { { q: } }
  let(:service_object) { described_class.new(**params) }

  specify 'validations' do
    %i[q].each do |attr|
      expect(service_object).to validate_presence_of(attr)
    end
  end

  describe '#call' do
    subject { service_object.call }

    let(:render_stub) { instance_double('ActionController::Base', render_to_string: 'rendered_template_body') }
    before { allow(ActionController::Base).to receive(:new).and_return(render_stub) }

    context 'valid loan credit data hash' do
      it 'returns a rendered credit template with loan inquiry data' do
        subject

        expect(service_object.body).to match('rendered_template_body')
        expect(service_object.status).to eq(200)

        expected_render_arguments = {
          template: 'credit_disclosures/show',
          layout: nil,
          locals: {
            borrower: {
              first_name: loan_credit_data.loan_inquiry.application['first_name'],
              last_name: loan_credit_data.loan_inquiry.application['last_name']
            },
            loan_credit_data: {
              created_at: DateHelper.to_ct_date_string(loan_credit_data.created_at),
              credit_report_date: DateHelper.to_ct_date_string(loan_credit_data.credit_report_date),
              credit_report_rank_pct: loan_credit_data.credit_report_rank_pct,
              credit_report_score: loan_credit_data.credit_report_score,
              unified_id: loan_credit_data.loan&.unified_id,
              is_experian: loan_credit_data.beyond_request_tracking_id.nil?
            }
          }
        }
        expect(render_stub).to have_received(:render_to_string).with(expected_render_arguments)
      end

      it 'returns a rendered credit template with borrower data' do
        loan_credit_data.update!(borrower: create(:borrower), loan_inquiry_id: nil)

        subject

        expect(service_object.body).to match('rendered_template_body')
        expect(service_object.status).to eq(200)

        expected_render_arguments = {
          template: 'credit_disclosures/show',
          layout: nil,
          locals: {
            borrower: {
              first_name: loan_credit_data.borrower.first_name,
              last_name: loan_credit_data.borrower.last_name
            },
            loan_credit_data: {
              created_at: DateHelper.to_ct_date_string(loan_credit_data.created_at),
              credit_report_date: DateHelper.to_ct_date_string(loan_credit_data.credit_report_date),
              credit_report_rank_pct: loan_credit_data.credit_report_rank_pct,
              credit_report_score: loan_credit_data.credit_report_score,
              unified_id: loan_credit_data.loan&.unified_id,
              is_experian: loan_credit_data.beyond_request_tracking_id.nil?
            }
          }
        }
        expect(render_stub).to have_received(:render_to_string).with(expected_render_arguments)
      end

      it 'returns a rendered credit template with no borrower details' do
        loan_credit_data.update!(loan_inquiry_id: nil)

        subject

        expect(service_object.body).to match('rendered_template_body')
        expect(service_object.status).to eq(200)

        expected_render_arguments = {
          template: 'credit_disclosures/show',
          layout: nil,
          locals: {
            borrower: {},
            loan_credit_data: {
              created_at: DateHelper.to_ct_date_string(loan_credit_data.created_at),
              credit_report_date: DateHelper.to_ct_date_string(loan_credit_data.credit_report_date),
              credit_report_rank_pct: loan_credit_data.credit_report_rank_pct,
              credit_report_score: loan_credit_data.credit_report_score,
              unified_id: loan_credit_data.loan&.unified_id,
              is_experian: loan_credit_data.beyond_request_tracking_id.nil?
            }
          }
        }
        expect(render_stub).to have_received(:render_to_string).with(expected_render_arguments)
      end

      context 'when the credit report date is nil' do
        let(:loan_credit_data) { create(:loan_credit_data, created_at: 1.day.ago, loan:, loan_inquiry: nil) }

        it 'returns the created_at date' do
          subject

          expect(service_object.body).to match('rendered_template_body')
          expect(service_object.status).to eq(200)

          expected_render_arguments = {
            template: 'credit_disclosures/show',
            layout: nil,
            locals: {
              borrower: {},
              loan_credit_data: {
                created_at: DateHelper.to_ct_date_string(loan_credit_data.created_at),
                credit_report_date: DateHelper.to_ct_date_string(loan_credit_data.created_at),
                credit_report_rank_pct: loan_credit_data.credit_report_rank_pct,
                credit_report_score: loan_credit_data.credit_report_score,
                unified_id: loan_credit_data.loan&.unified_id,
                is_experian: loan_credit_data.beyond_request_tracking_id.nil?
              }
            }
          }
          expect(render_stub).to have_received(:render_to_string).with(expected_render_arguments)
        end
      end
    end

    context 'invalid loan' do
      let(:q) { SecureRandom.uuid }

      it 'returns a not found response' do
        subject

        expect(service_object.body).to include('Not Found')
        expect(service_object.status).to eq(404)
      end
    end

    context 'when an error occurs during rendering' do
      before { allow(render_stub).to receive(:render_to_string).and_raise(StandardError.new('Error during rendering')) }

      it 'returns a 500 error' do
        subject

        expect(service_object.body).to include('Error during credit disclosure rendering')
        expect(service_object.status).to eq(500)
      end
    end
  end
end
