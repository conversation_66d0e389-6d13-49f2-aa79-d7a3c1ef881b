# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::DocumentsController, type: :request do
  let(:code) { 'Wv1F5Q' }
  let(:service_entity) { 'bf' }
  let!(:landing_lead) { create(:landing_lead, lead_code: code) }
  let!(:borrower) { create(:borrower, email: landing_lead.email) }
  let!(:loan) { create(:loan, :onboarded, :investor_assigned, borrower:, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE) }
  let!(:other_borrower) { create(:borrower) }
  let!(:doc) { create(:doc, loan:) }
  let(:session) { { code:, service_entity:, borrower_id: borrower.id } }

  let(:tiny_pdf) { "%PDF-1.4\ntrailer<</Root<</Pages<</Kids[<</MediaBox[0 0 3 3]>>]>>>>>>" }
  let(:s3_client) do
    Aws::S3::Client.new(stub_responses: { get_object: { body: tiny_pdf } })
  end
  before do
    mock_session!(session)

    allow(Users::CheckAccountActivation).to receive(:call).and_return(false)
    allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
  end

  describe '#show' do
    it 'renders' do
      get servicing_document_path(doc)

      expect(response).to be_successful
    end

    it 'redirects to authentication if user is not logged in' do
      mock_session!({})

      get servicing_document_path(doc)

      expect(response).to redirect_to(signin_borrowers_path(redirect: servicing_document_path(doc)))
    end

    it 'records events' do
      expect(Documents::Show).to receive(:call).with(document: doc)

      get servicing_document_path(doc)

      expect_request_event_record('get_servicing_documents_show')
    end

    it 'returns 404 if user is logged in but does not own this loan' do
      mock_session!(session.merge(borrower_id: other_borrower.id))

      get servicing_document_path(doc)

      expect(response.status).to eq(404)
    end

    it 'redirects to whoops on error' do
      expect(Documents::Show).to receive(:call).and_raise('Boom!')

      get servicing_document_path(doc)

      expect_request_event_record('get_servicing_documents_show')

      expect(response).to redirect_to(whoops_servicing_dashboard_index_path(offer: code, s: service_entity))

      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end
  end

  describe '#amortization' do
    let!(:loanpro_loan) { create(:loanpro_loan, loan:, til_sign_date: Date.today) }
    let(:cache_key) { "loanpro_amortization_#{loan.unified_id}" }
    let(:loanpro_report_job) { double('Loanpro::GenerateDynamicReportJob') }
    let(:cached_data) { { id: '111', status: 'in_progress' } }

    before do
      allow(Loanpro::GenerateDynamicReportJob).to receive(:new).and_return(loanpro_report_job)
      allow(loanpro_report_job).to receive(:report_url).and_return(nil)
    end

    it 'redirects to the file if a URL was provided' do
      allow(loanpro_report_job).to receive(:report_url).and_return('http://example.com/report.pdf')

      get amortization_servicing_documents_path

      expect(response).to redirect_to('http://example.com/report.pdf')
    end

    it 'does not trigger a new job' do
      expect(Loanpro::GenerateDynamicReportJob).not_to receive(:perform_async)

      cache = double('Rails.cache')
      allow(Rails).to receive(:cache).and_return(cache)
      allow(cache).to receive(:fetch).with(cache_key, version: Date.today, expires_in: 24.hours).and_return(cached_data)

      get amortization_servicing_documents_path

      expect(response.status).to eq(200)
      expect(response.body).to include('channels--loanpro-report-uuid-value="111"')
    end

    it 'triggers a new job' do
      expect(Loanpro::GenerateDynamicReportJob).to receive(:perform_async)

      get amortization_servicing_documents_path
      uuid = Rails.cache.read(cache_key)[:id]

      expect(response.status).to eq(200)
      expect(response.body).to include(%(channels--loanpro-report-uuid-value="#{uuid}"))
    end

    it 'gracefully handles the case of URL exception' do
      allow(loanpro_report_job).to receive(:report_url).and_raise(Loanpro::GenerateDynamicReportJob::ProcessFailedError)

      get amortization_servicing_documents_path
      uuid = Rails.cache.read(cache_key)[:id]

      expect(response.status).to eq(200)
      expect(response.body).to include(%(channels--loanpro-report-uuid-value="#{uuid}"))
    end
  end
end
