# frozen_string_literal: true

# == Schema Information
#
# Table name: talkdesk_events
#
#  id                       :uuid             not null, primary key
#  call_disposition_payload :jsonb            not null
#  called_at                :timestamptz
#  direction                :string
#  disposition              :string
#  enqueued_at              :timestamptz
#  event                    :string
#  loan_app_status          :string
#  phone_number             :string
#  removed_at               :timestamptz
#  service_entity           :string
#  created_at               :timestamptz
#  updated_at               :timestamptz
#  loan_id                  :uuid
#  talkdesk_call_id         :uuid
#  talkdesk_list_id         :string
#  talkdesk_record_id       :uuid
#  unified_id               :string
#
# Indexes
#
#  index_talkdesk_events_on_loan_id       (loan_id)
#  index_talkdesk_events_on_phone_number  (phone_number)
#
# Foreign Keys
#
#  fk_rails_...  (loan_id => loans.id)
#
require 'rails_helper'

RSpec.describe TalkdeskEvent, type: :model do
  describe 'relationships' do
    it { should belong_to(:loan).optional }
  end

  describe 'validations' do
    it 'creates a valid talkdesk event' do
      expect do
        talkdesk_event = TalkdeskEvent.create!(
          direction: TalkdeskEvent::OUTBOUND_DIRECTION,
          disposition: TalkdeskEvent::QUEUED_DISPOSITION,
          talkdesk_record_id: SecureRandom.uuid
        )

        expect(talkdesk_event.call_disposition_payload).to eq({})
      end.to change(TalkdeskEvent, :count).by(1)
    end

    it do
      should define_enum_for(:direction)
        .with_values(
          INBOUND: 'INBOUND',
          OUTBOUND: 'OUTBOUND'
        )
        .backed_by_column_of_type(:string)
    end

    it do
      should define_enum_for(:disposition)
        .with_values(
          QUEUED: 'QUEUED',
          QUEUE_REMOVED: 'QUEUE_REMOVED',
          NO_CONTACT: 'NO_CONTACT',
          BASIC_CONTACT: 'BASIC_CONTACT',
          AWAITING_ACTION: 'AWAITING_ACTION',
          TERMINAL: 'TERMINAL',
          UNKNOWN: 'UNKNOWN'
        )
        .backed_by_column_of_type(:string)
    end
  end

  describe '#call_disposition_payload' do
    it 'stores nested objects as json' do
      call_disposition_payload = { call_id: SecureRandom.uuid, call_duration: 120, misc: [{ json: 'field' }] }
      talkdesk_event = create(:talkdesk_event,
                              call_disposition_payload:)

      expect(talkdesk_event.reload.call_disposition_payload).to eq(call_disposition_payload.deep_stringify_keys)

      # We've seen regressions in certain json serializers storing nested json as strings
      # notably in especially store_model > 1.6.2, < 2.1.1
      entities_type = TalkdeskEvent.select("jsonb_typeof(call_disposition_payload->'misc') as typeof").where(id: talkdesk_event.id).first
      expect(entities_type.typeof).to eq('array')
      entity_type = TalkdeskEvent.select("jsonb_typeof(call_disposition_payload->'misc'->0) as typeof").where(id: talkdesk_event.id).first
      expect(entity_type.typeof).to eq('object')
    end
  end
end
