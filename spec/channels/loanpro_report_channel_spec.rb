# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproReportChannel, type: :channel do
  let(:borrower) { create(:borrower) }
  let(:uuid) { SecureRandom.uuid }

  before { create(:loan, borrower: borrower) }

  context 'authenticated' do
    before { stub_connection current_borrower: borrower }

    it 'successfully subscribes' do
      subscribe uuid: uuid
      expect(subscription).to be_confirmed
    end
  end

  context 'unauthenticated' do
    before { stub_connection current_borrower: nil }

    it 'rejects subscribtion' do
      subscribe uuid: uuid
      expect(subscription).to be_rejected
    end
  end
end
