# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_07_15_133934) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"
  enable_extension "uuid-ossp"

  create_table "apr_calculations", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "offer_id"
    t.string "external_offer_id", limit: 100
    t.decimal "apr", precision: 15, scale: 2
    t.string "description", limit: 255
    t.timestamptz "expiration_date", null: false
    t.decimal "interest_rate", precision: 15, scale: 3
    t.decimal "initial_term_payment", precision: 8, scale: 2
    t.decimal "final_term_payment", precision: 8, scale: 2
    t.decimal "loan_amount", precision: 15, scale: 2, null: false
    t.decimal "origination_fee", precision: 15, scale: 2
    t.decimal "origination_fee_percent", precision: 8, scale: 2
    t.boolean "selected", default: false
    t.decimal "sum_of_payments", precision: 15, scale: 2, null: false
    t.string "term"
    t.string "term_frequency", limit: 36
    t.string "calculated_by", comment: "Calculation by QuickQuote or AprCalculator"
    t.boolean "used", default: false, comment: "Indicates if this was used by the system"
    t.json "api_payloads", default: {}, null: false, comment: "api payloads used for this calculation."
    t.json "api_responses", default: {}, null: false, comment: "api responses for debugging."
    t.timestamptz "created_at"
    t.index ["offer_id"], name: "index_apr_calculations_on_offer_id"
  end

  create_table "arix_funding_statuses", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "arix_loan_id"
    t.uuid "loan_id", null: false
    t.string "funding_status"
    t.string "submission_status"
    t.jsonb "validation_errors", default: []
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at", null: false
    t.index ["arix_loan_id"], name: "index_arix_funding_statuses_on_arix_loan_id"
    t.index ["loan_id"], name: "index_arix_funding_statuses_on_loan_id"
  end

  create_table "bank_accounts", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "borrower_id", null: false
    t.string "plaid_access_token", limit: 100
    t.string "plaid_item_id", limit: 100
    t.string "username", limit: 100
    t.string "holder_firstname", limit: 100
    t.string "holder_lastname", limit: 100
    t.string "routing_number", limit: 100, null: false
    t.string "account_number", limit: 100, null: false
    t.string "last_four_routing_number", limit: 4, null: false
    t.string "last_four_account_number", limit: 4, null: false
    t.string "bank", limit: 100, null: false
    t.boolean "enabled", default: true
    t.boolean "allow_verify", default: true
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.string "account_type", limit: 100
    t.string "plaid_account_id", limit: 100
    t.boolean "fund_transfer_authorize"
    t.uuid "loan_id", null: false
    t.index ["borrower_id", "loan_id", "routing_number", "account_number"], name: "bank_accounts_borrower_id_loan_id_routing_number_account_number", unique: true
    t.index ["loan_id"], name: "index_bank_accounts_on_loan_id"
    t.index ["plaid_item_id"], name: "index_bank_accounts_on_plaid_item_id"
  end

  create_table "borrower_aditional_info", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "borrower_id"
    t.string "address_street", limit: 100
    t.string "address_apt", limit: 100
    t.string "city", limit: 50
    t.string "state", limit: 2
    t.string "zip_code", limit: 5
    t.string "phone_number", limit: 10
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.uuid "loan_id"
    t.string "spouse_first_name"
    t.string "spouse_last_name"
    t.string "spouse_address_street"
    t.string "spouse_address_apt"
    t.string "spouse_city"
    t.string "spouse_state"
    t.string "spouse_zip_code"
    t.boolean "married", null: false
    t.index ["borrower_id"], name: "index_borrower_aditional_info_on_borrower_id"
    t.index ["loan_id"], name: "index_borrower_aditional_info_on_loan_id"
  end

  create_table "borrowers", id: :uuid, default: nil, force: :cascade do |t|
    t.string "first_name", limit: 100, null: false
    t.string "last_name", limit: 100, null: false
    t.text "status", default: "unverified"
    t.string "email", limit: 100, null: false
    t.date "date_of_birth"
    t.string "ssn", limit: 11
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.text "token"
    t.string "identity_id", limit: 255
    t.datetime "privacy_accepted_at"
    t.datetime "tcpa_accepted_at"
    t.timestamptz "do_not_call_requested_at"
    t.index "lower((email)::text)", name: "borrowers_email_lowercase_idx"
    t.index ["email"], name: "borrowers_email_unique", unique: true
    t.check_constraint "status = ANY (ARRAY['verified'::text, 'unverified'::text, 'blocked'::text])", name: "borrowers_status_check"
  end

  create_table "contact_leads", id: :uuid, default: nil, force: :cascade do |t|
    t.string "first_name", limit: 255, null: false
    t.string "last_name", limit: 255, null: false
    t.string "phone_number", limit: 20, null: false
    t.string "email", limit: 255, null: false
    t.string "state", limit: 2, null: false
    t.decimal "loan_amount", precision: 15, scale: 2, null: false
    t.string "loan_purpose", limit: 50, default: "debt_consolidation"
    t.boolean "is_sent", default: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
  end

  create_table "contact_leads_tracking_parameters", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "contact_lead_id", null: false
    t.string "affiliate_id", limit: 10
    t.string "campaign_id", limit: 10
    t.string "device", limit: 200
    t.string "utm_source", limit: 200
    t.string "utm_medium", limit: 200
    t.string "utm_campaign", limit: 200
    t.string "utm_term", limit: 200
    t.string "subid2", limit: 200
    t.string "subid3", limit: 200
    t.string "subid4", limit: 200
    t.string "subid5", limit: 200
    t.string "subid6", limit: 200
    t.string "gclid", limit: 255
    t.string "gclsrc", limit: 255
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.index ["contact_lead_id"], name: "contact_leads_tracking_parameters_contact_lead_id_unique", unique: true
  end

  create_table "contact_preferences", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.boolean "do_not_call", default: false
    t.boolean "bankruptcy", default: false
    t.boolean "attorney_retained", default: false
    t.boolean "cease_and_desist", default: false
    t.uuid "loan_id", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.boolean "unsubscribe_sendgrid", default: false
    t.boolean "unsubscribe_sbt", default: false
    t.timestamptz "unsubscribe_timestamp"
    t.boolean "debt_settlement_company", default: false
    t.index ["loan_id"], name: "index_contact_preferences_on_loan_id"
  end

  create_table "crb_failure_reasons", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "crb_loan_id", null: false
    t.text "rule_name"
    t.text "rule"
    t.text "data"
    t.integer "failed_compliance_id"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.integer "rule_type"
    t.boolean "result"
  end

  create_table "crb_loan_status_history", id: :uuid, default: nil, force: :cascade do |t|
    t.string "old_status", limit: 255, null: false
    t.string "new_status", limit: 255, null: false
    t.uuid "crb_loan_id", null: false
    t.string "timestamp", limit: 255
    t.timestamptz "updated_at", null: false
  end

  create_table "crb_loans", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.uuid "crb_loan_id", null: false
    t.integer "crb_status_id"
    t.jsonb "raw_response", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.index ["loan_id"], name: "index_crb_loans_on_loan_id"
  end

  create_table "crb_statuses", id: :integer, default: nil, force: :cascade do |t|
    t.string "name", limit: 100
    t.string "description", limit: 500
  end

  create_table "delayed_tasks", id: :uuid, default: -> { "uuid_generate_v4()" }, force: :cascade do |t|
    t.string "type", limit: 255, null: false
    t.jsonb "context"
    t.boolean "processed", default: false
    t.timestamptz "execute_at", null: false
    t.timestamptz "created_at", default: -> { "now()" }, null: false
    t.timestamptz "updated_at", default: -> { "now()" }, null: false
  end

  create_table "dm2_leads", id: :uuid, default: nil, force: :cascade do |t|
    t.string "code", limit: 50, null: false
    t.string "project_number", limit: 50, null: false
    t.timestamptz "expiration_date", null: false
    t.timestamptz "mail_date"
    t.timestamptz "in_home_date"
    t.string "first_name", limit: 255, null: false
    t.string "middle_name", limit: 255
    t.string "last_name", limit: 255, null: false
    t.string "suffix", limit: 255
    t.string "address", limit: 100, null: false
    t.string "city", limit: 50, null: false
    t.string "state", limit: 2, null: false
    t.string "zip_code", limit: 5, null: false
    t.string "zip_4", limit: 4
    t.date "date_of_birth"
    t.string "ssn_last_7", limit: 9, null: false
    t.string "personal_loan_segment", limit: 50, null: false
    t.string "fico_segment", limit: 50, null: false
    t.string "revolving_balance_segment", limit: 50, null: false
    t.string "unsecured_installment_debt_segment", limit: 50, null: false
    t.string "total_unsecured_debt_segment", limit: 50, null: false
    t.string "utilization_segment", limit: 50, null: false
    t.string "installment_debt_balance", limit: 50, null: false
    t.string "revolving_debt_balance", limit: 50, null: false
    t.decimal "total_unsecured_debt_balance", precision: 50, scale: 4, null: false
    t.decimal "utilization_unsecured_debt", precision: 50, scale: 4, null: false
    t.integer "personal_loan_score", null: false
    t.integer "fico", null: false
    t.json "tradeline_details", null: false
    t.boolean "code_used", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.index "lower((last_name)::text)", name: "dm2_leads_last_name_lowercase_idx"
    t.index "substr((ssn_last_7)::text, (length((ssn_last_7)::text) - 3), 4)", name: "dm2_leads_ssn_last_7_substr_last_4_idx"
    t.index ["code"], name: "dm2_leads_code_unique", unique: true
    t.index ["created_at"], name: "dm2_leads_created_at_index"
  end

  create_table "dm2_prospect_tracking_parameters", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "dm2_prospect_id", null: false
    t.string "affiliate_id", limit: 10
    t.string "campaign_id", limit: 10
    t.string "device", limit: 200
    t.string "utm_source", limit: 200
    t.string "utm_medium", limit: 200
    t.string "utm_campaign", limit: 200
    t.string "utm_term", limit: 200
    t.string "subid2", limit: 200
    t.string "subid3", limit: 200
    t.string "subid4", limit: 200
    t.string "subid5", limit: 200
    t.string "subid6", limit: 200
    t.string "gclid", limit: 255
    t.string "gclsrc", limit: 255
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.index ["dm2_prospect_id"], name: "dm2_prospect_tracking_parameters_dm2_prospect_id_unique", unique: true
  end

  create_table "dm2_prospects", id: :uuid, default: nil, force: :cascade do |t|
    t.string "code", limit: 50, null: false
    t.string "first_name", limit: 255, null: false
    t.string "last_name", limit: 255, null: false
    t.string "phone_number", limit: 20, null: false
    t.string "email", limit: 255, null: false
    t.string "state", limit: 2, null: false
    t.decimal "loan_amount", precision: 15, scale: 2, null: false
    t.boolean "is_sent", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.uuid "beyond_lead"
  end

  create_table "doc_templates", id: :uuid, default: nil, force: :cascade do |t|
    t.string "name", limit: 200, null: false
    t.integer "version", null: false
    t.string "uri", limit: 255
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.text "body"
    t.string "type", limit: 50, null: false
    t.text "doc_content_type", default: "html"
    t.string "states", limit: 2, array: true
    t.index "name, version, ((states IS NULL))", name: "doc_templates_null_state", unique: true, where: "(states IS NULL)"
    t.index ["states"], name: "doc_templates_states_index", using: :gin
    t.index ["version", "type", "states"], name: "doc_templates_version_type_states_unique", unique: true
  end

  create_table "docs", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "template_id"
    t.text "name", null: false
    t.string "ip_address", limit: 255
    t.uuid "loan_id"
    t.string "uri", limit: 255, null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.uuid "loan_inquiry_id"
    t.index ["loan_id"], name: "index_docs_on_loan_id"
    t.index ["loan_inquiry_id"], name: "index_docs_on_loan_inquiry_id"
  end

  create_table "document_mails", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "doc_id", null: false
    t.string "doc_template_type", null: false
    t.string "postgrid_letter_id", null: false
    t.string "status", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at", null: false
    t.index ["doc_id"], name: "index_document_mails_on_doc_id"
  end

  create_table "email_validations", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "email", limit: 100, null: false
    t.string "verdict"
    t.string "source"
    t.decimal "score", precision: 5, scale: 5
    t.jsonb "checks", default: {}
    t.string "ip_address"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at", null: false
    t.index ["email"], name: "index_email_validations_on_email"
  end

  create_table "events", force: :cascade do |t|
    t.string "type"
    t.string "name"
    t.string "request_id"
    t.jsonb "data", default: {}
    t.jsonb "metadata", default: {}
    t.jsonb "response", default: {}
    t.timestamptz "created_at"
    t.index ["name"], name: "index_events_on_name"
    t.index ["request_id"], name: "index_events_on_request_id"
    t.index ["type"], name: "index_events_on_type"
  end

  create_table "experiment_subjects", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "experiment", limit: 100, null: false
    t.string "cohort", limit: 50, null: false
    t.string "subject_type", limit: 100, null: false
    t.uuid "subject_id", null: false
    t.timestamptz "created_at", null: false
    t.index ["cohort"], name: "index_experiment_subjects_on_cohort"
    t.index ["experiment", "subject_id"], name: "index_experiment_subjects_on_experiment_and_subject_id", unique: true
    t.index ["experiment"], name: "index_experiment_subjects_on_experiment"
    t.index ["subject_id", "subject_type"], name: "index_experiment_subjects_on_subject_id_and_type"
  end

  create_table "external_apps", id: :uuid, default: nil, force: :cascade do |t|
    t.string "client_id", limit: 50, null: false
    t.string "client_secret", limit: 255, null: false
    t.string "name", limit: 255, null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.string "code", limit: 250
    t.string "type", limit: 250
  end

  create_table "investors", id: :uuid, default: nil, force: :cascade do |t|
    t.string "name", limit: 100, null: false
    t.string "title", limit: 100, null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.integer "subportfolio_id", null: false
    t.datetime "disabled_at"
  end

  create_table "knex_migrations", id: :serial, force: :cascade do |t|
    t.string "name", limit: 255
    t.integer "batch"
    t.timestamptz "migration_time"
  end

  create_table "knex_migrations_lock", primary_key: "index", id: :serial, force: :cascade do |t|
    t.integer "is_locked"
  end

  create_table "landing_leads", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "first_name", limit: 255
    t.string "last_name", limit: 255
    t.string "phone_number", limit: 20
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "url", limit: 255
    t.string "lead_code", limit: 50
    t.string "email"
    t.datetime "tcpa_accepted_at"
    t.string "service_entity_name", limit: 50
    t.datetime "privacy_accepted_at"
    t.index ["created_at"], name: "index_landing_leads_on_created_at"
    t.index ["lead_code"], name: "index_landing_leads_on_lead_code"
  end

  create_table "leads", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "code", limit: 50, null: false
    t.string "type", limit: 100, null: false
    t.string "project_number", limit: 50
    t.string "first_name", limit: 255, null: false
    t.string "middle_name", limit: 255
    t.string "last_name", limit: 255, null: false
    t.string "suffix_name", limit: 10
    t.string "address", limit: 255
    t.string "city", limit: 255
    t.string "state", limit: 2
    t.string "zip_4", limit: 4
    t.string "zip_code", limit: 5
    t.decimal "total_unsecured_debt", precision: 8, scale: 2
    t.decimal "utilization_unsecured_debt", precision: 8, scale: 2
    t.integer "personal_loan_score"
    t.integer "fico_score"
    t.string "phone_number", limit: 20
    t.timestamptz "expiration_date"
    t.json "data"
    t.boolean "code_used", default: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.string "client_id", limit: 50
    t.string "email", limit: 255
    t.date "date_of_birth"
    t.string "ssn", limit: 11
    t.string "cft_account_details", limit: 255
    t.string "account_number", limit: 255
    t.json "loan_details"
    t.json "payment_details"
    t.json "tradeline_details"
    t.string "program_id", limit: 12
    t.integer "months_since_enrollment"
    t.date "next_payment_date"
    t.text "cft_bank_name"
    t.text "cft_account_holder_name"
    t.string "service_entity_name", limit: 255, default: "Beyond Finance", null: false
    t.string "nu_dse_holder_s_name_c", limit: 255
    t.string "nu_dse_bank_name_c", limit: 255
    t.decimal "nu_dse_bank_account_number_c", precision: 20, default: "0"
    t.decimal "nu_dse_routing_number_c", precision: 15, default: "0"
    t.string "nu_dse_account_type_c", limit: 255
    t.index "lower((email)::text) DESC", name: "leads_email_lowercase_idx"
    t.index "upper((code)::text)", name: "leads_code_uppercase_idx"
    t.index ["code", "type"], name: "leads_code_type_unique", unique: true
    t.index ["email"], name: "leads_email_index"
  end

  create_table "loan_app_statuses", id: :serial, force: :cascade do |t|
    t.string "name", limit: 100
    t.index ["name"], name: "loan_app_statuses_name_idx"
  end

  create_table "loan_concentration_logs", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.uuid "created_by_id"
    t.string "event"
    t.string "message"
    t.timestamptz "created_at", null: false
    t.index ["loan_id"], name: "index_loan_concentration_logs_on_loan_id"
  end

  create_table "loan_credit_data", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id"
    t.uuid "borrower_id"
    t.string "hash", limit: 128
    t.string "credit_report_id", limit: 36
    t.date "credit_report_date"
    t.integer "credit_report_score"
    t.float "credit_report_rank_pct"
    t.timestamptz "created_at", default: -> { "CURRENT_TIMESTAMP" }
    t.timestamptz "updated_at", default: -> { "CURRENT_TIMESTAMP" }
    t.json "credit_report_raw_json"
    t.uuid "beyond_request_tracking_id"
    t.uuid "loan_inquiry_id"
    t.index ["borrower_id"], name: "index_loan_credit_data_on_borrower_id"
    t.index ["hash"], name: "loan_credit_data_hash_unique", unique: true
    t.index ["loan_id"], name: "index_loan_credit_data_on_loan_id"
    t.index ["loan_inquiry_id"], name: "index_loan_credit_data_on_loan_inquiry_id"
  end

  create_table "loan_details", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.decimal "amount_financed", precision: 8, scale: 2, null: false
    t.decimal "estimated_beyond_fees", precision: 8, scale: 2, default: "0.0"
    t.decimal "estimated_cft_deposits", precision: 8, scale: 2
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.decimal "total_amount_enrolled_debt", precision: 8, scale: 2
    t.date "beyond_enrollment_date"
    t.string "beyond_enrollment_status", limit: 50
    t.integer "nsfs_3_months"
    t.decimal "payment_adherence_ratio_3_months", precision: 8, scale: 2
    t.decimal "payment_adherence_ratio_6_months", precision: 8, scale: 2
    t.decimal "months_since_enrollment", precision: 8, scale: 2
    t.integer "consecutive_payments_count", default: 0, null: false
    t.decimal "lifetime_payment_adherence", precision: 16, scale: 8, default: "0.0", null: false
    t.integer "nsfs_4_months", default: 0, null: false
    t.integer "nsfs_9_months", default: 0, null: false
    t.integer "nsfs_12_months", default: 0, null: false
    t.integer "nsfs_18_months", default: 0, null: false
    t.integer "nsfs_24_months", default: 0, null: false
    t.integer "nsfs_lifetime", default: 0, null: false
    t.decimal "payment_adherence_ratio_4_months", precision: 8, scale: 2, default: "0.0", null: false
    t.integer "nsfs_6_months", default: 0, null: false
    t.string "eligibility_level", limit: 1
    t.string "cft_account_holder_name"
    t.string "cft_account_number"
    t.string "cft_account_details"
    t.string "cft_bank_name"
    t.integer "hard_inquiries_60_days"
    t.integer "hard_inquiries_90_days"
    t.boolean "credit_freeze_active", default: false
    t.timestamptz "credit_freeze_first_seen_at"
    t.string "credit_model_level"
    t.decimal "credit_model_score", precision: 6, scale: 5
    t.string "decision_champion", comment: "The champion decison value. [OFFERED or FRONT_END_DECLINED]"
    t.string "decision_challenger", comment: "The champion decison value. [OFFERED or FRONT_END_DECLINED]"
    t.index ["loan_id"], name: "index_loan_details_on_loan_id"
  end

  create_table "loan_inquiries", id: :uuid, default: nil, force: :cascade do |t|
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.string "gds_request_id", limit: 100, null: false
    t.string "beyond_request_tracking_id", limit: 100, null: false
    t.jsonb "application", null: false
    t.jsonb "decline"
    t.jsonb "offers"
    t.uuid "loan_id"
    t.index ["beyond_request_tracking_id"], name: "loan_inquiries_beyond_request_tracking_id_unique", unique: true
    t.index ["gds_request_id"], name: "loan_inquiries_gds_request_id_unique", unique: true
    t.index ["loan_id"], name: "index_loan_inquiries_on_loan_id"
  end

  create_table "loan_offer_reminders", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.timestamptz "sent_at", null: false
    t.integer "level"
    t.index ["loan_id"], name: "index_loan_offer_reminders_on_loan_id"
  end

  create_table "loan_payment_details", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.decimal "beyond_payment_amount", precision: 8, scale: 2, null: false
    t.string "beyond_payment_frequency", limit: 255
    t.json "beyond_payment_dates"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.decimal "monthly_deposit_amount", precision: 8, scale: 2
    t.decimal "estimated_payoff_amount", precision: 8, scale: 2
    t.index ["loan_id"], name: "loan_payment_details_loan_id_idx"
  end

  create_table "loan_status_history", id: :uuid, default: nil, force: :cascade do |t|
    t.string "old_status", limit: 255, null: false
    t.string "new_status", limit: 255, null: false
    t.uuid "loan_id", null: false
    t.string "request_id", limit: 255
    t.string "unified_id", limit: 255
    t.timestamptz "updated_at", null: false
    t.index ["loan_id"], name: "index_loan_status_history_on_loan_id"
  end

  create_table "loan_tradeline_details", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.decimal "tradeline_estimated_settlement_amount", precision: 8, scale: 2, null: false
    t.string "tradeline_account_number", limit: 255
    t.string "tradeline_name", limit: 255
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.string "original_creditor", limit: 255
    t.decimal "settlement_percent", precision: 8, scale: 2
    t.string "settled_tradelined_flag", limit: 100
    t.index ["loan_id"], name: "index_loan_tradeline_details_on_loan_id"
  end

  create_table "loanpro_customers", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "borrower_id", null: false
    t.string "loanpro_customer_id", limit: 100, null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.index ["borrower_id", "loanpro_customer_id"], name: "loanpro_customers_borrower_id_loanpro_customer_id_unique", unique: true
    t.index ["borrower_id"], name: "loanpro_customers_borrower_id_unique", unique: true
  end

  create_table "loanpro_loans", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.string "loanpro_loan_id", limit: 100, null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.text "loanpro_raw_response"
    t.timestamptz "til_sign_date"
    t.string "display_id", limit: 255
    t.uuid "offer_id"
    t.date "contract_date"
    t.timestamptz "contract_started_at"
    t.timestamptz "contract_generated_at"
    t.index ["loan_id"], name: "index_loanpro_loans_on_loan_id"
    t.index ["loanpro_loan_id"], name: "index_loanpro_loans_on_loanpro_loan_id"
    t.index ["offer_id"], name: "index_loanpro_loans_on_offer_id"
  end

  create_table "loans", id: :uuid, default: nil, force: :cascade do |t|
    t.string "request_id", limit: 100
    t.decimal "amount", precision: 15, scale: 2, null: false
    t.string "purpose", limit: 50, default: "debt_consolidation"
    t.string "credit_score_range", limit: 20
    t.string "employment_status", limit: 20
    t.decimal "anual_income", precision: 15, scale: 2
    t.string "housing_status", limit: 50
    t.string "time_at_residence", limit: 20
    t.uuid "borrower_id"
    t.timestamptz "expiration_date"
    t.integer "loan_app_status_id"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.string "code", limit: 100
    t.string "education_level", limit: 100
    t.string "product_type", limit: 100, default: "PPC"
    t.string "employment_pay_frecuency", limit: 100
    t.boolean "affiliate_sharing"
    t.string "source_type", limit: 10
    t.string "decision_reason_number", limit: 100
    t.text "decline_reason_text"
    t.decimal "credit_score", precision: 8, scale: 2
    t.text "score_factor"
    t.decimal "monthly_housing_payment", precision: 50, scale: 2
    t.string "employment_start_date", limit: 50
    t.string "employment_industry", limit: 150
    t.string "unified_id", limit: 255
    t.boolean "requested_offers", default: false
    t.string "program_id", limit: 12
    t.uuid "investor_id"
    t.string "originating_party", limit: 50
    t.boolean "should_send_adverse_action", default: false, null: false
    t.timestamptz "adverse_action_sent"
    t.text "beyond_salesforce"
    t.jsonb "decline_reasons"
    t.string "campaign", limit: 255
    t.text "affiliate_lead_id"
    t.decimal "dti", precision: 16, scale: 8, default: "0.0", null: false
    t.date "bankruptcy_filed_date"
    t.boolean "screened_for_bankruptcy", default: false, null: false
    t.boolean "exclude_from_allocation", default: false, null: false
    t.boolean "crb_dry_run", default: false, null: false
    t.integer "credit_inquiries_last_6_months"
    t.date "last_paycheck_on"
    t.decimal "monthly_deposit_amount", precision: 15, scale: 2
    t.integer "program_duration_in_tmonths"
    t.decimal "verified_income", precision: 15, scale: 2
    t.decimal "verified_dti", precision: 16, scale: 8
    t.string "arix_onboarding_status"
    t.uuid "contract_signing_token"
    t.datetime "excluded_from_concentration_at"
    t.decimal "verified_income_ratio", precision: 16, scale: 8
    t.index "upper((code)::text)", name: "loans_code_uppercase_idx"
    t.index ["borrower_id"], name: "loans_borrower_id_idx"
    t.index ["contract_signing_token"], name: "index_loans_on_contract_signing_token", unique: true
    t.index ["excluded_from_concentration_at"], name: "index_loans_on_excluded_from_concentration_at"
    t.index ["investor_id"], name: "index_loans_on_investor_id"
    t.index ["loan_app_status_id"], name: "index_loans_on_loan_app_status_id"
    t.index ["product_type"], name: "loans_product_type_idx"
    t.index ["program_id"], name: "index_loans_on_program_id"
    t.index ["request_id"], name: "loans_request_id_unique", unique: true
    t.index ["unified_id"], name: "loans_unified_id_unique", unique: true
  end

  create_table "ocrolus_reports", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.string "report_type", limit: 50, null: false
    t.jsonb "response", null: false
    t.string "ocrolus_id", limit: 255
    t.uuid "uploaded_todo_doc_id"
    t.timestamptz "created_at", null: false
    t.index ["loan_id"], name: "index_ocrolus_reports_on_loan_id"
    t.index ["ocrolus_id"], name: "index_ocrolus_reports_on_ocrolus_id"
  end

  create_table "offers", id: :uuid, default: nil, force: :cascade do |t|
    t.string "external_offer_id", limit: 100, null: false
    t.string "uri", limit: 250
    t.integer "sort_order"
    t.boolean "is_hero", default: false
    t.boolean "selected", default: false
    t.date "external_creation_date"
    t.timestamptz "expiration_date", null: false
    t.string "lender", limit: 50
    t.string "lender_logo_uri", limit: 100
    t.decimal "amount", precision: 15, scale: 2, null: false
    t.string "term", limit: 500
    t.decimal "monthly_payment", precision: 15, scale: 2
    t.decimal "interest_rate", precision: 15, scale: 3
    t.decimal "apr", precision: 15, scale: 2
    t.decimal "origination_fee", precision: 15, scale: 2
    t.uuid "loan_id", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.string "type", limit: 500, default: "regular"
    t.text "lender_description"
    t.text "lender_disclaimer"
    t.decimal "amount_financed", precision: 8, scale: 2
    t.decimal "origination_fee_percent", precision: 8, scale: 2
    t.decimal "total_advance_period_interest", precision: 8, scale: 2
    t.decimal "advanced_period_interest_per_term", precision: 8, scale: 2
    t.decimal "initial_term_payment", precision: 8, scale: 2
    t.decimal "final_term_payment", precision: 8, scale: 2
    t.string "originating_party", limit: 255, null: false
    t.decimal "settlement_amount", precision: 15, scale: 2
    t.decimal "cashout_amount", precision: 15, scale: 2
    t.string "even_lead_uuid", limit: 255
    t.string "description", limit: 255
    t.string "lender_network", limit: 50
    t.string "term_frequency", limit: 36
    t.boolean "shown_to_customer"
    t.index ["external_offer_id"], name: "offers_external_offer_id_index"
    t.index ["loan_id"], name: "offers_loan_id_idx"
  end

  create_table "plaid_reports", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.uuid "bank_account_id", null: false
    t.string "report_type", limit: 50, null: false
    t.jsonb "response", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.string "plaid_id", limit: 255
    t.index ["bank_account_id"], name: "index_plaid_reports_on_bank_account_id"
    t.index ["loan_id"], name: "index_plaid_reports_on_loan_id"
    t.index ["plaid_id"], name: "index_plaid_reports_on_plaid_id"
  end

  create_table "product_type_states", id: :serial, force: :cascade do |t|
    t.integer "product_type_id", null: false
    t.integer "state_id", null: false
    t.timestamptz "created_at"
    t.index ["state_id"], name: "index_product_type_states_on_state_id"
  end

  create_table "product_types", id: :serial, force: :cascade do |t|
    t.string "code", limit: 20, null: false
    t.string "name", limit: 150
    t.boolean "active", default: true, null: false
  end

  create_table "request_audit", id: :uuid, default: nil, force: :cascade do |t|
    t.text "url", null: false
    t.string "method", limit: 50, null: false
    t.string "request_id", limit: 50
    t.text "headers"
    t.text "body"
    t.timestamptz "created_at", null: false
  end

  create_table "seed_migration_data_migrations", force: :cascade do |t|
    t.string "version"
    t.integer "runtime"
    t.datetime "migrated_on"
  end

  create_table "socure_alerts", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "socure_reference_id", limit: 255, null: false
    t.string "alert_id", limit: 50
    t.string "entity_id", limit: 50
    t.string "reason", limit: 50
    t.string "reason_codes", limit: 50
    t.string "reviewed_by", limit: 25
    t.timestamptz "reviewed_at"
    t.text "notes"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.index ["alert_id"], name: "index_socure_alerts_on_alert_id"
    t.index ["entity_id"], name: "index_socure_alerts_on_entity_id"
    t.index ["socure_reference_id"], name: "index_socure_alerts_on_socure_reference_id"
  end

  create_table "socure_monitorings", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.string "socure_reference_id", limit: 255
    t.boolean "monitoring_enabled", default: false, null: false
    t.string "source", limit: 20
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.index ["loan_id"], name: "index_socure_monitorings_on_loan_id"
    t.index ["socure_reference_id"], name: "index_socure_monitorings_on_socure_reference_id"
  end

  create_table "socure_transaction_histories", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "socure_monitoring_id", null: false
    t.boolean "old_status", null: false
    t.boolean "new_status", null: false
    t.text "notes"
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.index ["socure_monitoring_id"], name: "index_socure_transaction_histories_on_socure_monitoring_id"
  end

  create_table "states", id: :serial, force: :cascade do |t|
    t.string "code", limit: 2, null: false
    t.string "name", limit: 30, null: false
  end

  create_table "talkdesk_events", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "loan_id"
    t.string "unified_id"
    t.string "loan_app_status"
    t.string "phone_number"
    t.string "talkdesk_list_id"
    t.string "service_entity"
    t.string "event"
    t.timestamptz "created_at"
    t.uuid "talkdesk_record_id"
    t.string "direction"
    t.timestamptz "enqueued_at"
    t.timestamptz "removed_at"
    t.timestamptz "called_at"
    t.string "disposition"
    t.jsonb "call_disposition_payload", default: {}, null: false
    t.timestamptz "updated_at"
    t.uuid "talkdesk_call_id"
    t.index ["loan_id"], name: "index_talkdesk_events_on_loan_id"
    t.index ["phone_number"], name: "index_talkdesk_events_on_phone_number"
  end

  create_table "temporary_token", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id"
    t.text "token", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "expiration_date", null: false
    t.string "type", limit: 255, null: false
    t.index ["loan_id"], name: "index_temporary_token_on_loan_id"
  end

  create_table "til_history", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.jsonb "til_data", null: false
    t.uuid "docusign_envelope_id", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.timestamptz "signed_at"
    t.uuid "docusign_webhook_id", default: -> { "uuid_generate_v4()" }, null: false
    t.timestamptz "voided_at"
    t.index ["loan_id"], name: "index_til_history_on_loan_id"
  end

  create_table "todo_docs", id: { type: :string, limit: 64 }, force: :cascade do |t|
    t.string "todo_id", limit: 64
    t.string "name", limit: 300, null: false
    t.string "url", limit: 450, null: false
    t.string "status", limit: 100, null: false
    t.string "rejected_reason", limit: 100
    t.string "mime_type", limit: 100
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.string "external_id", limit: 50
    t.string "s3_bucket", limit: 255
    t.string "s3_key", limit: 255
    t.index ["external_id"], name: "todo_docs_external_id_unique", unique: true
    t.index ["todo_id"], name: "index_todo_docs_on_todo_id"
  end

  create_table "todo_verification_details", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "documents", default: [], array: true
    t.text "reason"
    t.string "rule_id"
    t.string "logic", null: false
    t.string "todo_id", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.index ["rule_id"], name: "index_todo_verification_details_on_rule_id"
    t.index ["todo_id"], name: "index_todo_verification_details_on_todo_id"
  end

  create_table "todos", id: { type: :string, limit: 64 }, force: :cascade do |t|
    t.uuid "loan_id"
    t.string "type", limit: 50, null: false
    t.string "status", limit: 50, null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.string "external_id", limit: 50
    t.datetime "automated_verification_started_at", precision: nil
    t.datetime "automated_verification_completed_at", precision: nil
    t.index ["external_id"], name: "todos_external_id_unique", unique: true
    t.index ["loan_id"], name: "index_todos_on_loan_id"
    t.index ["type", "status"], name: "index_todos_on_type_and_status"
  end

  create_table "tracking_parameters", id: :uuid, default: nil, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.string "affiliate_id", limit: 10
    t.string "campaign_id", limit: 10
    t.string "device", limit: 200
    t.string "utm_source", limit: 200
    t.string "utm_medium", limit: 200
    t.string "utm_campaign", limit: 200
    t.string "utm_term", limit: 200
    t.string "subid2", limit: 200
    t.string "subid3", limit: 200
    t.string "subid4", limit: 200
    t.string "subid5", limit: 200
    t.string "subid6", limit: 200
    t.string "gclid", limit: 255
    t.string "gclsrc", limit: 255
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.timestamptz "deleted_at"
    t.index ["loan_id"], name: "tracking_parameters_loan_id_unique", unique: true
  end

  create_table "users", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "first_name", default: "", null: false
    t.string "last_name", default: "", null: false
    t.string "status", default: "legacy"
    t.string "service_entity_name", default: "Beyond Finance"
    t.boolean "activated_account", default: false, null: false
    t.datetime "activated_at"
    t.integer "failed_attempts", default: 0
    t.timestamptz "locked_at"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "verification_inputs", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "loan_id", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.decimal "bank_account_balance_current", precision: 15, scale: 2, comment: "The current balance reported for the borrower's selected bank account."
    t.decimal "bank_account_balance_30_days_ago", precision: 15, scale: 2, comment: "The balance of the borrower's selected bank account 30 days ago. A value of NULL is used in cases when the bank account does not have 30 days worth of balance history."
    t.integer "nsfs_30_days", comment: "A count of the number of NSF transactions reported in the borrowers selected bank account during the past 30 days."
    t.integer "overdrafts_30_days", comment: "A count of the number of overdraft protection transactions reported in the borrowers selected bank account during the past 30 days."
    t.integer "negative_daily_balances_30_days", comment: "A count of the number of days within the past 30 days for which a negative ending balance was reported on the selected bank account for this loan."
    t.string "source", default: "plaid", null: false, comment: "Identifies the primary source of data used to perform this set of verification checks."
    t.date "bank_statement_end_date", comment: "The ending date of the timeframe covered by the most recent bank statement for this application."
    t.integer "bank_statement_authenticity_scores", comment: "A set of scores representing the authenticity of the documents submitted by the borrower", array: true
    t.jsonb "matching_account", comment: "A set of attributes describing the bank account that was matched during creation of verification inputs"
    t.boolean "personal_loan_deposits", comment: "A flag indicating whether a disqualifying number of personal loan deposit transactions were found"
    t.boolean "high_cost_payday_cash_advance_deposits", comment: "A flag indicating whether a disqualifying number of high cost payday cash advance deposit transactions were found"
    t.boolean "high_cost_payday_cash_advance_payments", comment: "A flag indicating whether a disqualifying number of high cost payday cash advance payment transactions were found"
    t.uuid "verification_version_id"
    t.integer "nsf_count_by_description", comment: "A count of the number of NSF transactions found in the bank statement details from the past 30 days as determined based on the presence of various NSF or overdraft related keywords in the transaction description"
    t.string "credit_model_level", comment: "The credit model level returned from offer generation."
    t.index ["loan_id"], name: "index_verification_inputs_on_loan_id"
  end

  create_table "verification_results", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "verification_inputs_id", null: false
    t.string "bank_statement_result", null: false
    t.jsonb "rules_output", null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at"
    t.index ["verification_inputs_id"], name: "index_verification_results_on_verification_inputs_id"
  end

  create_table "verification_versions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "version", null: false, comment: "Semantic Verification Version"
    t.string "git_sha", null: false, comment: "AMS git sha for this version"
    t.jsonb "feature_flags", default: {}, null: false
    t.timestamptz "created_at", null: false
    t.timestamptz "updated_at", null: false
    t.index ["created_at"], name: "index_verification_versions_on_created_at", order: :desc
    t.index ["git_sha"], name: "index_verification_versions_on_git_sha"
    t.index ["version"], name: "index_verification_versions_on_version", unique: true
  end

  add_foreign_key "apr_calculations", "offers"
  add_foreign_key "arix_funding_statuses", "loans"
  add_foreign_key "bank_accounts", "borrowers", name: "FK-borrower-bank-accounts"
  add_foreign_key "bank_accounts", "loans", name: "FK-loan-bank-accounts"
  add_foreign_key "borrower_aditional_info", "borrowers", name: "FK-borrower-aditional-info"
  add_foreign_key "borrower_aditional_info", "loans", name: "FK-loan-borrower-aditional-info"
  add_foreign_key "contact_leads_tracking_parameters", "contact_leads", name: "FK-tracking-contact-leads"
  add_foreign_key "contact_preferences", "loans"
  add_foreign_key "crb_failure_reasons", "crb_loans", name: "FK-crb_loan-crb_failure_reasons"
  add_foreign_key "crb_loan_status_history", "crb_loans", name: "FK-crb_loans-table"
  add_foreign_key "crb_loans", "crb_statuses"
  add_foreign_key "crb_loans", "loans", name: "FK-loan-crb-loan"
  add_foreign_key "dm2_prospect_tracking_parameters", "dm2_prospects", name: "FK-tracking-parameters-dm2-prospects"
  add_foreign_key "docs", "doc_templates", column: "template_id", name: "FK-docs-template-relation"
  add_foreign_key "docs", "loan_inquiries", validate: false
  add_foreign_key "docs", "loans", name: "FK-docs-loan-relation"
  add_foreign_key "document_mails", "docs"
  add_foreign_key "loan_concentration_logs", "loans"
  add_foreign_key "loan_credit_data", "borrowers", name: "FK-borrower-id-loan-credit-data"
  add_foreign_key "loan_credit_data", "loan_inquiries"
  add_foreign_key "loan_credit_data", "loans", name: "FK-loan-id-loan-credit-data"
  add_foreign_key "loan_details", "loans", name: "FK-loan-details"
  add_foreign_key "loan_inquiries", "loans", name: "FK-loan-loan_inquiries"
  add_foreign_key "loan_offer_reminders", "loans", name: "FK-loan-loan-offer-reminders"
  add_foreign_key "loan_payment_details", "loans", name: "FK-loan-payment-details"
  add_foreign_key "loan_status_history", "loans", name: "FK-loan-table"
  add_foreign_key "loan_tradeline_details", "loans", name: "FK-loan-tradeline_details"
  add_foreign_key "loanpro_customers", "borrowers", name: "FK-borrower-loanpro-customer"
  add_foreign_key "loanpro_loans", "loans", name: "FK-loan-loanpro-loan"
  add_foreign_key "loanpro_loans", "offers"
  add_foreign_key "loans", "borrowers", name: "FK-borrower-loans"
  add_foreign_key "loans", "investors", name: "FK-loan-owner"
  add_foreign_key "loans", "loan_app_statuses", name: "FK-loan-app-status"
  add_foreign_key "ocrolus_reports", "loans"
  add_foreign_key "offers", "loans", name: "FK-loan-offer"
  add_foreign_key "plaid_reports", "bank_accounts"
  add_foreign_key "plaid_reports", "loans"
  add_foreign_key "product_type_states", "product_types", name: "FK-product_type"
  add_foreign_key "product_type_states", "states", name: "FK-state"
  add_foreign_key "socure_monitorings", "loans"
  add_foreign_key "socure_transaction_histories", "socure_monitorings"
  add_foreign_key "talkdesk_events", "loans", validate: false
  add_foreign_key "temporary_token", "loans", name: "FK-temporary_token-loan-relation"
  add_foreign_key "til_history", "loans", name: "FK-loan-til-history"
  add_foreign_key "todo_docs", "todos", name: "FK-docs-todo-relation"
  add_foreign_key "todo_verification_details", "todos"
  add_foreign_key "todos", "loans", name: "FK-todos-loan-relation"
  add_foreign_key "tracking_parameters", "loans", name: "FK-tracking-loans"
  add_foreign_key "verification_inputs", "loans"
  add_foreign_key "verification_inputs", "verification_versions"
  add_foreign_key "verification_results", "verification_inputs", column: "verification_inputs_id"

  create_view "ams_loan_credit_data", sql_definition: <<-SQL
      SELECT loan_credit_data.id,
      loan_credit_data.loan_id,
      loan_credit_data.borrower_id,
      loan_credit_data.hash AS credit_report_hash,
      loan_credit_data.credit_report_id,
      loan_credit_data.credit_report_date,
      loan_credit_data.credit_report_score,
      loan_credit_data.credit_report_rank_pct,
      loan_credit_data.created_at,
      loan_credit_data.updated_at,
      loan_credit_data.credit_report_raw_json,
      loan_credit_data.beyond_request_tracking_id,
      loan_credit_data.loan_inquiry_id
     FROM loan_credit_data;
  SQL
end
