# frozen_string_literal: true

class AddMissingForeignKeys < ActiveRecord::Migration[7.2]
  def change
    say 'Adding foreign keys to large tables without validation to avoid locking'
    add_foreign_key :docs, :loan_inquiries, column: :loan_inquiry_id, validate: false
    add_foreign_key :talkdesk_events, :loans, column: :loan_id, validate: false

    say 'Adding foreign keys to smaller tables with immediate validation'
    add_foreign_key :loan_credit_data, :loan_inquiries, column: :loan_inquiry_id
    add_foreign_key :loanpro_loans, :offers, column: :offer_id
    add_foreign_key :apr_calculations, :offers, column: :offer_id
  end
end
