# frozen_string_literal: true

class DropAuditsTable < ActiveRecord::Migration[7.2]
  def up
    drop_table :audits, if_exists: true
  end

  def down
    create_table :audits, id: :uuid, default: nil do |t|
      t.string :origin, limit: 50, null: false
      t.json :old_data, null: false
      t.json :new_data, null: false
      t.uuid :borrower_id
      t.timestamptz :created_at, null: false
      t.timestamptz :updated_at
    end
  end
end
