# frozen_string_literal: true

class LoanproReportChannel < ApplicationCable::Channel
  def subscribed
    return reject unless current_borrower

    stream_from "loanpro_report:#{current_borrower.id}:#{params[:uuid]}"
  end

  def after_confirmation_sent
    ActionCable.server.broadcast("loanpro_report:#{current_borrower.id}:#{params[:uuid]}", cached_data)
  end

  private

  def cached_data
    Rails.cache.read("loanpro_amortization_#{current_loan.unified_id}", version: Date.today)
  end

  def current_loan
    @current_loan ||= Loan.current_for_borrower_by_id(current_borrower.id)
  end
end
