# frozen_string_literal: true

# == Schema Information
#
# Table name: talkdesk_events
#
#  id                       :uuid             not null, primary key
#  call_disposition_payload :jsonb            not null
#  called_at                :timestamptz
#  direction                :string
#  disposition              :string
#  enqueued_at              :timestamptz
#  event                    :string
#  loan_app_status          :string
#  phone_number             :string
#  removed_at               :timestamptz
#  service_entity           :string
#  created_at               :timestamptz
#  updated_at               :timestamptz
#  loan_id                  :uuid
#  talkdesk_call_id         :uuid
#  talkdesk_list_id         :string
#  talkdesk_record_id       :uuid
#  unified_id               :string
#
# Indexes
#
#  index_talkdesk_events_on_loan_id       (loan_id)
#  index_talkdesk_events_on_phone_number  (phone_number)
#
# Foreign Keys
#
#  fk_rails_...  (loan_id => loans.id)
#
class TalkdeskEvent < ApplicationRecord
  TYPES = %w[added removed].freeze

  DIRECTIONS = [
    INBOUND_DIRECTION = 'INBOUND',
    OUTBOUND_DIRECTION = 'OUTBOUND'
  ].freeze

  DISPOSITIONS = [
    QUEUED_DISPOSITION = 'QUEUED',
    QUEUE_REMOVED_DISPOSITION = 'QUEUE_REMOVED',
    NO_CONTACT_DISPOSITION = 'NO_CONTACT',
    BASIC_CONTACT_DISPOSITION = 'BASIC_CONTACT',
    AWAITING_ACTION_DISPOSITION = 'AWAITING_ACTION',
    TERMINAL_DISPOSITION = 'TERMINAL',
    UNKNOWN_DISPOSITION = 'UNKNOWN'
  ].freeze

  belongs_to :loan, required: false

  # TODO:  Remove `event`, `service_entity`, `unified_id` after
  #        deploying work to stop writing to this field.
  enum :event, TYPES.index_by(&:to_sym)

  enum :direction, DIRECTIONS.index_by(&:to_sym)
  enum :disposition, DISPOSITIONS.index_by(&:to_sym)
end
