# frozen_string_literal: true

module Servicing
  class DocumentsComponent < ApplicationComponent
    FakeDocument = Struct.new(:id, :name)

    def initialize(documents:)
      @documents = documents.to_a
      @documents.push(FakeDocument.new('amortization', 'Amortization.pdf'))

      super
    end

    def desktop_headers
      ['Document Name', 'Download']
    end

    def desktop_rows
      @documents.map do |document|
        {
          'Document Name' => friendly_document_name(document.name),
          'Download' => download_link(document)
        }
      end
    end

    def mobile_headers
      ['Document Name']
    end

    def mobile_rows
      @documents.map do |document|
        {
          'Document Name' => %(
            #{friendly_document_name(document.name)}<br>
            <div class="d-inline-block mt-1">
              #{download_link(document)}
            </div>
          ).html_safe
        }
      end
    end

    private

    def friendly_document_name(name)
      # Name format is DOCTITLE_version_0_FIRSTNAME_LASTNAME_UUID.pdf
      regex = /(.*)_version_\d+_(.*)_[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}.*(\.pdf)/
      matches = name.match(regex)

      # If we don't have an exact match to our pattern, return the original filename until we address this.
      return name if !matches || matches.length != 4

      "#{matches[1]}#{matches[3]}"
    end

    def download_link(document)
      render DocumentLinkComponent.new(document:)
    end
  end
end
