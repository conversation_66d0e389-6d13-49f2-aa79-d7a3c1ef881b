# frozen_string_literal: true

module Loanpro
  class GenerateDynamicReportJob < ApplicationJob
    class ProcessFailedError < StandardError; end

    MAX_ATTEMPTS = 10
    VALID_STATUS = %w[dataDumpProcess.status.complete dataDumpProcess.status.inProgress].freeze

    TEMPLATES = {
      amortization: Rails.application.config_for(:loanpro_api).dig(:dynamic_templates, :amortization)
    }.freeze

    attr_reader :loanpro_loan_id, :requires_new_report

    def perform(uuid, loanpro_loan_id, template, attempt = 0)
      @loanpro_loan_id = loanpro_loan_id
      raise ProcessFailedError if attempt == MAX_ATTEMPTS

      url = report_url
      return broadcast_result(uuid, url) if url.present?

      request_new_report(template) if requires_new_report
      self.class.perform_async(uuid, loanpro_loan_id, template, attempt + 1, wait: 5.seconds)
    rescue ProcessFailedError
      broadcast_failure(uuid)
      Rails.logger.error(<<~MSG.squish)
        LoanPro report generation failed for loanpro_loan_id: #{loanpro_loan_id},
        loan: #{loan.unified_id}, attempt: #{attempt}
      MSG
    end

    def report_url(loanpro_loan_id = @loanpro_loan_id)
      files = Clients::LoanproApi.download_dynamic_template(loanpro_loan_id)
      url, date, status = files.dig('results', 0).try(:values_at, 'url', 'created', 'status')
      raise ProcessFailedError if status && VALID_STATUS.exclude?(status)

      @requires_new_report = date.blank? || Date.today != LoanproHelper.parse_date(date)
      url if url.present? && !requires_new_report && status == 'dataDumpProcess.status.complete'
    end

    def request_new_report(template)
      Clients::LoanproApi.generate_dynamic_template(loanpro_loan_id, TEMPLATES[template.to_sym])
    end

    def broadcast_result(uuid, url)
      data = { id: uuid, status: 'complete', url: url }

      Rails.cache.write("loanpro_amortization_#{loan.unified_id}", data, version: Date.today)
      ActionCable.server.broadcast("loanpro_report:#{loan.borrower_id}:#{uuid}", data)
    end

    def broadcast_failure(uuid)
      data = { id: uuid, status: 'failed' }

      Rails.cache.write("loanpro_amortization_#{loan.unified_id}", data, version: Date.today)
      ActionCable.server.broadcast("loanpro_report:#{loan.borrower_id}:#{uuid}", data)
    end

    def loan
      @loan ||= LoanproLoan.find_by!(loanpro_loan_id: loanpro_loan_id).loan
    end
  end
end
