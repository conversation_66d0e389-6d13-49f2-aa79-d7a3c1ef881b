import { Controller } from "@hotwired/stimulus"
import consumer from "../../channels/consumer"

export default class extends Controller {
  static values = { uuid: String };

  receivedMessage(payload) {
    if (payload.status === "complete") {
      window.location.href = payload.url;
    } else if (payload.status === "failed") {
      alert("Report generation failed. Please try again later.");
    }
  }

  connect() {
    this.channel = consumer.subscriptions.create(
      { channel: "LoanproReportChannel", uuid: this.uuidValue },
      { received: (data) => this.receivedMessage(data) }
    );
  }

  disconnect() {
    this.channel.unsubscribe();
  }
}
