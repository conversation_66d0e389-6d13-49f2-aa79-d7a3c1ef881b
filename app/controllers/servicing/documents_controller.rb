# frozen_string_literal: true

module Servicing
  class DocumentsController < BaseController
    class AmortizationError < StandardError; end

    def show
      document = ::Doc.find_by!(id: params[:id], loan: current_loan)
      data = ::Documents::Show.call(document:)
      send_data(data, filename: document.name, type: 'application/pdf', disposition: :attachment)
    rescue ActiveRecord::RecordNotFound
      route_not_found
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def amortization
      url = current_amortization_url
      return redirect_to(url, allow_other_host: true) if url.present?

      cache_key = "loanpro_amortization_#{current_loan.unified_id}"
      @data = Rails.cache.fetch(cache_key, version: Date.today, expires_in: 24.hours) do
        uuid = SecureRandom.uuid
        Loanpro::GenerateDynamicReportJob.perform_async(uuid, loanpro_loan.loanpro_loan_id, 'amortization')
        { id: uuid, status: 'in_progress' }
      end
    end

    private

    def current_amortization_url
      Loanpro::GenerateDynamicReportJob.new.report_url(loanpro_loan.loanpro_loan_id)
    rescue Loanpro::GenerateDynamicReportJob::ProcessFailedError
      nil # Just move forward because the rest of the process will handle the error
    end
  end
end
